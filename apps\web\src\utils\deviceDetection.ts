/**
 * 设备检测工具函数
 * 用于检测移动设备和浏览器特性
 */

/**
 * 检测是否为移动设备
 */
export const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false

  // 检测用户代理字符串
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android',
    'webos',
    'iphone',
    'ipad',
    'ipod',
    'blackberry',
    'windows phone',
    'mobile',
  ]

  const isMobileUA = mobileKeywords.some((keyword) => userAgent.includes(keyword))

  // 检测触摸支持
  const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0

  // 检测屏幕尺寸 - 支持各种移动设备和平板设备
  const screenWidth = window.innerWidth
  const screenHeight = window.innerHeight
  const minDimension = Math.min(screenWidth, screenHeight)
  const maxDimension = Math.max(screenWidth, screenHeight)

  const isSmallScreen = minDimension <= 768
  const isTabletScreen = minDimension >= 768 && minDimension <= 1440 && maxDimension <= 2048

  // 移动设备检测逻辑：
  // 1. 用户代理包含移动设备关键词
  // 2. 或者同时满足：有触摸支持 + (小屏幕或平板屏幕)
  return isMobileUA || (hasTouchSupport && (isSmallScreen || isTabletScreen))
}

/**
 * 检测是否为平板设备
 */
export const isTabletDevice = (): boolean => {
  if (typeof window === 'undefined') return false

  // 检测用户代理字符串中的平板关键词
  const userAgent = navigator.userAgent.toLowerCase()
  const tabletKeywords = ['ipad', 'tablet', 'kindle']
  const isTabletUA = tabletKeywords.some((keyword) => userAgent.includes(keyword))

  // 检测触摸支持
  const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0

  // 平板设备屏幕尺寸范围
  const screenWidth = window.innerWidth
  const screenHeight = window.innerHeight

  // 考虑横屏和竖屏两种情况，使用较小和较大尺寸来判断
  const minDimension = Math.min(screenWidth, screenHeight)
  const maxDimension = Math.max(screenWidth, screenHeight)

  // 平板设备特征：
  // - 较小尺寸（通常是宽度）在768-1440px之间
  // - 较大尺寸（通常是高度）在600-2048px之间，支持高分辨率设备
  const isTabletSize =
    minDimension >= 768 && minDimension <= 1440 && maxDimension >= 600 && maxDimension <= 2048

  return isTabletUA || (hasTouchSupport && isTabletSize)
}

/**
 * 检测是否为 iOS 设备
 */
export const isIOS = (): boolean => {
  if (typeof window === 'undefined') return false

  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

/**
 * 检测是否为 Android 设备
 */
export const isAndroid = (): boolean => {
  if (typeof window === 'undefined') return false

  return /Android/.test(navigator.userAgent)
}

/**
 * 检测是否支持 Visual Viewport API
 */
export const supportsVisualViewport = (): boolean => {
  if (typeof window === 'undefined') return false

  return 'visualViewport' in window
}

/**
 * 检测是否为 Safari 浏览器
 */
export const isSafari = (): boolean => {
  if (typeof window === 'undefined') return false

  const userAgent = navigator.userAgent.toLowerCase()
  return userAgent.includes('safari') && !userAgent.includes('chrome')
}

/**
 * 检测是否为微信内置浏览器
 */
export const isWeChatBrowser = (): boolean => {
  if (typeof window === 'undefined') return false

  return /MicroMessenger/i.test(navigator.userAgent)
}

/**
 * 获取设备像素比
 */
export const getDevicePixelRatio = (): number => {
  if (typeof window === 'undefined') return 1

  return window.devicePixelRatio || 1
}

/**
 * 检测是否支持 CSS env() 函数（安全区域）
 */
export const supportsEnvFunction = (): boolean => {
  if (typeof window === 'undefined') return false

  try {
    return CSS.supports('padding', 'env(safe-area-inset-top)')
  } catch {
    return false
  }
}
